/**
 * Test script to verify Whereby room sharing functionality
 * Run this with: node test-whereby-room-sharing.js
 */

const https = require('https')

// Test configuration
const TEST_LESSON_ID = 'test-lesson-123'
const API_BASE = 'http://localhost:3000' // Adjust if your dev server runs on different port

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url)
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }

    const protocol = urlObj.protocol === 'https:' ? https : require('http')
    const req = protocol.request(requestOptions, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        try {
          const response = {
            status: res.statusCode,
            data: data ? JSON.parse(data) : null
          }
          resolve(response)
        } catch (error) {
          reject(new Error(`JSON Parse Error: ${error.message}. Raw response: ${data}`))
        }
      })
    })

    req.on('error', reject)
    
    if (options.data) {
      req.write(JSON.stringify(options.data))
    }
    
    req.end()
  })
}

async function testRoomSharing() {
  console.log('🧪 Testing Whereby Room Sharing Functionality\n')

  try {
    // Test 1: Teacher creates room
    console.log('1️⃣ Teacher requesting room...')
    const teacherResponse = await makeRequest(`${API_BASE}/api/whereby/create-room`, {
      method: 'POST',
      data: {
        lessonId: TEST_LESSON_ID
      }
    })

    if (teacherResponse.status !== 200) {
      throw new Error(`Teacher room creation failed: ${teacherResponse.status}`)
    }

    const teacherRoom = teacherResponse.data.room
    console.log('✅ Teacher room created:', {
      meetingId: teacherRoom.meetingId,
      lessonId: teacherRoom.lessonId,
      isExisting: teacherResponse.data.isExisting || false
    })

    // Test 2: Student requests same room
    console.log('\n2️⃣ Student requesting same room...')
    const studentResponse = await makeRequest(`${API_BASE}/api/whereby/create-room`, {
      method: 'POST',
      data: {
        lessonId: TEST_LESSON_ID
      }
    })

    if (studentResponse.status !== 200) {
      throw new Error(`Student room request failed: ${studentResponse.status}`)
    }

    const studentRoom = studentResponse.data.room
    console.log('✅ Student room received:', {
      meetingId: studentRoom.meetingId,
      lessonId: studentRoom.lessonId,
      isExisting: studentResponse.data.isExisting || false
    })

    // Test 3: Verify both got the same room
    console.log('\n3️⃣ Verifying room sharing...')
    if (teacherRoom.meetingId === studentRoom.meetingId) {
      console.log('✅ SUCCESS: Both teacher and student got the same room!')
      console.log(`   Meeting ID: ${teacherRoom.meetingId}`)
      console.log(`   Teacher URL: ${teacherRoom.hostRoomUrl}`)
      console.log(`   Student URL: ${studentRoom.roomUrl}`)
    } else {
      console.log('❌ FAILURE: Teacher and student got different rooms!')
      console.log(`   Teacher Meeting ID: ${teacherRoom.meetingId}`)
      console.log(`   Student Meeting ID: ${studentRoom.meetingId}`)
    }

    // Test 4: Get room info
    console.log('\n4️⃣ Testing room info retrieval...')
    const roomInfoResponse = await makeRequest(`${API_BASE}/api/whereby/room/${TEST_LESSON_ID}`)
    
    if (roomInfoResponse.status === 200) {
      console.log('✅ Room info retrieved successfully:', {
        meetingId: roomInfoResponse.data.room.meetingId,
        isActive: roomInfoResponse.data.isActive
      })
    } else {
      console.log('❌ Room info retrieval failed:', roomInfoResponse.status)
    }

    console.log('\n🎉 Test completed successfully!')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    process.exit(1)
  }
}

// Run the test
if (require.main === module) {
  testRoomSharing()
}

module.exports = { testRoomSharing }
