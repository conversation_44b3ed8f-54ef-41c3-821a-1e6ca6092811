<template>
  <div :class="['toolbar', `toolbar--${role}`]" :style="style">
    <ul id="toolbar-buttons" ref="toolbar_buttons" class="toolbar-buttons">
      <!--      <li class="toolbar-button-wrapper toolbar-button-wrapper-replace">-->
      <!--        <button class="toolbar-button-item toolbar-button-replace cursor-pointer">-->
      <!--          <svg class="toolbar-button-icon" width="19" height="20" viewBox="0 0 19 20">-->
      <!--            <use xlink:href="/images/classroom/toolbar.svg#squareline"></use>-->
      <!--          </svg>-->
      <!--        </button>-->
      <!--      </li>-->
      <li class="toolbar-button-wrapper">
        <button
          id="toolbar-switch"
          data-toolbar-default-cursor
          :class="[
            'toolbar-button-item toolbar-button-pointer cursor-pointer',
            { selected: currentTool === 'pointer' },
          ]"
          :disabled="isLockedForStudent"
          @click="selectToolClickHandler('pointer', `cursor-pointer`)"
        >
          <svg
            class="toolbar-button-icon"
            width="32"
            height="34"
            viewBox="0 0 32 34"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#pointer`"
            ></use>
          </svg>
        </button>
        <div class="hover-btn-info">{{ $t('default_cursor') }}</div>
      </li>
      <li
        class="toolbar-button-wrapper toolbar-button-wrapper-pencil"
        @mouseleave.stop="currentHorizontalMenu = null"
      >
        <button
          :class="[
            'toolbar-button-item toolbar-button-hand cursor-pointer',
            {
              selected:
                currentTool === 'line' ||
                currentTool === 'circle' ||
                currentTool === 'triangle' ||
                currentTool === 'square' ||
                currentTool === 'pen',
            },
          ]"
          :disabled="isLockedForStudent"
          @click="currentHorizontalMenu = 'toolbar-horizontal'"
        >
          <svg
            class="toolbar-button-icon"
            width="33"
            height="33"
            viewBox="0 0 33 33"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#pencil`"
            ></use>
          </svg>
        </button>
        <div class="hover-btn-info">{{ $t('drawing') }}</div>

        <div
          :class="[
            'toolbar-buttons-horizontal',
            { 'toolbar-show': currentHorizontalMenu === 'toolbar-horizontal' },
          ]"
        >
          <ul>
            <li
              class="toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-line"
            >
              <button
                :class="[
                  'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                  { selected: currentTool === 'line' },
                ]"
                data-toolbar-tool-line
                @click="selectToolClickHandler('line', 'pencil-cursor')"
              >
                <svg
                  class="toolbar-button-icon"
                  width="39"
                  height="37"
                  viewBox="0 0 39 37"
                >
                  <use
                    :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#draw-line`"
                  ></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">
                {{ $t('draw_line') }}
              </div>
            </li>
            <li class="toolbar-button-wrapper-horizontal">
              <button
                :class="[
                  'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                  { selected: currentTool === 'circle' },
                ]"
                data-toolbar-tool-circle
                @click="selectToolClickHandler('circle', 'pencil-cursor')"
              >
                <svg
                  class="toolbar-button-icon"
                  width="36"
                  height="37"
                  viewBox="0 0 39 40"
                >
                  <use
                    :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#draw-circle`"
                  ></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">
                {{ $t('draw_circle') }}
              </div>
            </li>
            <li class="toolbar-button-wrapper-horizontal">
              <button
                :class="[
                  'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                  { selected: currentTool === 'triangle' },
                ]"
                data-toolbar-tool-triangle
                @click="selectToolClickHandler('triangle', 'pencil-cursor')"
              >
                <svg
                  class="toolbar-button-icon"
                  width="41"
                  height="34"
                  viewBox="0 0 41 34"
                >
                  <use
                    :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#draw-triangle`"
                  ></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">
                {{ $t('draw_triangle') }}
              </div>
            </li>
            <li class="toolbar-button-wrapper-horizontal">
              <button
                :class="[
                  'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                  { selected: currentTool === 'square' },
                ]"
                data-toolbar-tool-square
                @click="selectToolClickHandler('square', 'pencil-cursor')"
              >
                <svg
                  class="toolbar-button-icon"
                  width="36"
                  height="38"
                  viewBox="0 0 36 38"
                >
                  <use
                    :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#draw-square`"
                  ></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">
                {{ $t('draw_square') }}
              </div>
            </li>
            <li
              class="toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-pencil"
            >
              <button
                :class="[
                  'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                  { selected: currentTool === 'pen' },
                ]"
                data-toolbar-tool-pen
                @click="selectToolClickHandler('pen', 'pencil-cursor')"
              >
                <svg
                  class="toolbar-button-icon"
                  width="33"
                  height="33"
                  viewBox="0 0 33 33"
                >
                  <use
                    :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#pencil`"
                  ></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">
                {{ $t('enable_drawing_tool') }}
              </div>
            </li>
            <!--            <li class="toolbar-button-wrapper-horizontal">-->
            <!--              <button-->
            <!--                class="toolbar-button-item toolbar-button-item-hand toolbar-button-item-horizontal"-->
            <!--                @click="$root.$data.user.tool = 'hand';"-->
            <!--              >-->
            <!--                <svg class="toolbar-button-icon">-->
            <!--                  <use xlink:href="/images/classroom/toolbar.svg#hand"></use>-->
            <!--                </svg>-->
            <!--              </button>-->
            <!--              <div class="hover-btn-info hover-horizontal-button">Navigation tool</div>-->
            <!--            </li>-->
          </ul>
        </div>
      </li>
      <li class="toolbar-button-wrapper">
        <button
          :class="[
            'toolbar-button-item cursor-pointer',
            { selected: currentTool === 'eraser' },
          ]"
          data-toolbar-eraser
          :disabled="isLockedForStudent"
          @click="selectToolClickHandler('eraser', 'eraser-cursor')"
        >
          <svg
            class="toolbar-button-icon"
            width="35"
            height="31"
            viewBox="0 0 35 31"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#lastic`"
            ></use>
          </svg>
        </button>
        <div class="hover-btn-info">
          {{ $t('enable_erasing_tool') }}
        </div>
      </li>
      <li class="toolbar-button-wrapper">
        <button
          id="toolbar-button-video"
          class="toolbar-button-item cursor-pointer"
          data-toolbar-add-video
          :disabled="isLockedForStudent"
          @click="toggleVideoInput"
        >
          <svg
            class="toolbar-button-icon"
            width="39"
            height="31"
            viewBox="0 0 39 31"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#play`"
            ></use>
          </svg>
        </button>
        <div class="hover-btn-info">{{ $t('add_video') }}</div>
      </li>
      <!--      <li class="toolbar-button-wrapper">-->
      <!--        <button class="toolbar-button-item" @click="clearLines()">-->
      <!--          <svg class="toolbar-button-icon">-->
      <!--            <use xlink:href="/images/classroom/toolbar.svg#trash"></use>-->
      <!--          </svg>-->
      <!--        </button>-->
      <!--        <div class="hover-btn-info">Remove all lines</div>-->
      <!--      </li>-->
      <!--      <li class="toolbar-button-wrapper toolbar-button-wrapper-undo">-->
      <!--        <button class="toolbar-button-item toolbar-button-undo">-->
      <!--          <svg class="toolbar-button-icon">-->
      <!--            <use xlink:href="/images/classroom/toolbar.svg#undo"></use>-->
      <!--          </svg>-->
      <!--        </button>-->
      <!--        <div class="hover-btn-info">Undo</div>-->
      <!--      </li>-->
      <li v-if="isTeacher" class="toolbar-button-wrapper">
        <button
          class="toolbar-button-item cursor-pointer"
          data-toolbar-buzz-student
          :disabled="alertDisabled"
          @click.prevent="buzz"
        >
          <svg
            class="toolbar-button-icon"
            width="35"
            height="38"
            viewBox="0 0 35 38"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#ring`"
            ></use>
          </svg>
        </button>
        <div class="hover-btn-info">{{ $t('buzz_student') }}</div>
      </li>
      <!--      <li class="toolbar-button-wrapper">-->
      <!--        <button-->
      <!--          ref="toolbar-button-file"-->
      <!--          class="toolbar-button-item toolbar-button-file"-->
      <!--          @click="showHorizontalMenu('toolbar-horizontal-file', 'toolbar-button-file')"-->
      <!--        >-->
      <!--          <svg class="toolbar-button-icon">-->
      <!--            <use xlink:href="/images/classroom/toolbar.svg#file"></use>-->
      <!--          </svg>-->
      <!--        </button>-->
      <!--      </li>-->
      <li
        class="toolbar-button-wrapper"
        @mouseleave.stop="currentHorizontalMenu = null"
      >
        <button
          class="toolbar-button-item toolbar-button-hand cursor-pointer"
          @click="currentHorizontalMenu = 'toolbar-horizontal-file'"
        >
          <!--          <label-->
          <!--            class="popup-load-files-label-upload popup-load-files-label-upload-laptop"-->
          <!--          >-->
          <svg
            class="toolbar-button-icon"
            width="29"
            height="38"
            viewBox="0 0 29 38"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#library`"
            ></use>
          </svg>
          <!--            <input-->
          <!--              type="file"-->
          <!--              id="upload-library-files-laptop"-->
          <!--              multiple-->
          <!--              class="popup-load-files-btn-upload"-->
          <!--            />-->
          <!--          </label>-->
        </button>
        <div class="hover-btn-info">
          {{ $t('library') }}
        </div>

        <div
          :class="[
            'toolbar-buttons-horizontal toolbar-buttons-horizontal-file',
            {
              'toolbar-show':
                currentHorizontalMenu === 'toolbar-horizontal-file',
            },
          ]"
        >
          <ul>
            <li
              class="toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-books"
            >
              <button
                id="load-files-library"
                class="toolbar-button-item toolbar-button-item-horizontal cursor-pointer"
                data-toolbar-library
                @click="openLibrary"
              >
                <svg
                  class="toolbar-button-icon"
                  width="38"
                  height="38"
                  viewBox="0 0 38 38"
                >
                  <use
                    :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#books`"
                  ></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">
                {{ $t('select_from_library') }}
              </div>
            </li>
            <li
              class="toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-laptop"
            >
              <button
                class="toolbar-button-item toolbar-button-item-horizontal cursor-pointer"
                data-toolbar-computer
              >
                <label
                  class="popup-load-files-label-upload popup-load-files-label-upload-laptop"
                >
                  <svg
                    class="toolbar-button-icon"
                    width="41"
                    height="34"
                    viewBox="0 0 41 34"
                  >
                    <use
                      :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#laptop`"
                    ></use>
                  </svg>
                  <input
                    id="upload-library-files-laptop"
                    type="file"
                    multiple
                    :accept="acceptedFilesStr"
                    class="popup-load-files-btn-upload"
                    @change="uploadFromComputer"
                  />
                </label>
              </button>
              <div class="hover-btn-info hover-horizontal-button">
                {{ $t('upload_from_computer') }}
              </div>
            </li>
          </ul>
        </div>
      </li>
      <li v-if="isTeacher" class="toolbar-button-wrapper">
        <button
          class="toolbar-button-item cursor-pointer"
          data-toolbar-lock
          @click.prevent="toggleStudentRoomStatus"
        >
          <svg
            class="toolbar-button-icon"
            width="38"
            height="50"
            viewBox="0 0 38 50"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#${
                isLocked ? 'lock' : 'unlock'
              }`"
            ></use>
          </svg>
        </button>
        <div class="hover-btn-info">
          <template v-if="isLocked">
            {{ $t('enable_moving_resizing_drawing_for_student') }}
          </template>
          <template v-else>
            {{ $t('disable_moving_resizing_drawing_for_student') }}
          </template>
        </div>
      </li>
      <li class="toolbar-button-wrapper toolbar-button-wrapper-reset">
        <button
          class="toolbar-button-item cursor-pointer"
          data-toolbar-reset
          :disabled="isLockedForStudent"
          @click="reset"
        >
          <svg
            class="toolbar-button-icon"
            width="36"
            height="36"
            viewBox="0 0 37 37"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#restore`"
            ></use>
          </svg>
        </button>
        <div class="hover-btn-info">
          {{ $t('restore_whiteboard_video_to_original_positions') }}
        </div>
      </li>
      <li class="toolbar-button-wrapper toolbar-button-wrapper-exit">
        <button
          class="toolbar-button-item cursor-pointer"
          data-toolbar-exit
          @click="exitLesson"
        >
          <svg
            class="toolbar-button-icon"
            width="36"
            height="36"
            viewBox="0 0 37 37"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#exit`"
            ></use>
          </svg>
        </button>
        <div class="hover-btn-info">{{ $t('exit_class') }}</div>
      </li>
      <li
        v-if="isTeacher"
        class="toolbar-button-wrapper"
        @mouseleave.stop="currentHorizontalMenu = null"
      >
        <button
          class="toolbar-button-item toolbar-button-hand cursor-pointer"
          :disabled="isLessonFinished || !isFinishedAllowed"
          @click="currentHorizontalMenu = 'toolbar-horizontal-finish'"
        >
          <svg
            class="toolbar-button-icon"
            width="43"
            height="38"
            viewBox="0 0 43 38"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#tick`"
            ></use>
          </svg>
        </button>
        <div class="hover-btn-info">
          {{ $t(isLessonFinished ? 'class_finished' : 'finish_class') }}
        </div>

        <div
          :class="[
            'toolbar-buttons-horizontal toolbar-buttons-horizontal-file',
            {
              'toolbar-show':
                currentHorizontalMenu === 'toolbar-horizontal-finish',
            },
          ]"
        >
          <ul>
            <li
              class="toolbar-button-wrapper-horizontal toolbar-button-wrapper-finish"
            >
              <button
                class="toolbar-button-item toolbar-button-item-horizontal cursor-pointer"
                data-toolbar-finish
                type="submit"
                @click="finishLesson"
              >
                <svg
                  class="toolbar-button-icon"
                  width="43"
                  height="38"
                  viewBox="0 0 43 38"
                >
                  <use
                    :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#tick`"
                  ></use>
                </svg>
              </button>
              <div class="hover-btn-info hover-horizontal-button">
                {{ $t('finish_class') }}
              </div>
            </li>
          </ul>
        </div>
      </li>
      <li v-if="isStudent" class="toolbar-button-wrapper">
        <button class="toolbar-button-item cursor-pointer" disabled="disabled">
          <svg
            class="toolbar-button-icon"
            width="38"
            height="50"
            viewBox="0 0 38 50"
          >
            <use
              :xlink:href="`${require('~/assets/images/classroom/toolbar.svg')}#${
                isLocked ? 'lock' : 'unlock'
              }`"
            ></use>
          </svg>
        </button>
        <div class="hover-btn-info">
          <template v-if="isLocked">
            {{ $t('moving_resizing_drawing_are_disabled') }}
          </template>
          <template v-else>
            {{ $t('classroom_controls_are_unlocked') }}
          </template>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import {
  defaultWidth,
  mainCanvasWidth,
  mainCanvasHeight,
} from '~/helpers/constants'
import SetTool from '~/mixins/SetTool'
import UploadFiles from '~/mixins/UploadFiles'
import StatusOnline from '~/mixins/StatusOnline'

export default {
  name: 'Toolbar',
  mixins: [SetTool, UploadFiles, StatusOnline],
  props: {
    studentId: {
      type: String,
      required: true,
    },
    file: {
      type: Object,
      required: true,
    },
    viewportWidth: {
      type: Number,
      required: true,
    },
    viewportHeight: {
      type: Number,
      required: true,
    },
    scale: {
      type: Number,
      default: 1,
    },
    minZoom: {
      type: Number,
      required: true,
    },
    isFinishedAllowed: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      buzzed: false,
      currentTool: 'pointer',
      currentHorizontalMenu: null,
      offset: 5,
    }
  },
  computed: {
    isCanvasOversizeX() {
      return mainCanvasWidth > this.viewportWidth
    },
    isScaledCanvasOversizeX() {
      return mainCanvasWidth * this.scale > this.viewportWidth
    },
    isCanvasOversizeY() {
      return mainCanvasHeight > this.viewportHeight
    },
    isScaledCanvasOversizeY() {
      return mainCanvasHeight * this.scale > this.viewportHeight
    },
    style() {
      return {
        bottom: this.isScaledCanvasOversizeY
          ? '10px'
          : `${
              this.viewportHeight -
              mainCanvasHeight * this.scale +
              this.offset * 2
            }px`,
        right: this.isScaledCanvasOversizeX
          ? '10px'
          : `${
              this.viewportWidth -
              mainCanvasWidth * this.scale +
              this.offset * 2
            }px`,
      }
    },
    studentStatus() {
      // return this.$store.state.socket.connectedUserIds.includes(this.studentId)
      let status = 'offline'

      if (
        Object.prototype.hasOwnProperty.call(
          this.userStatuses,
          this.studentId?.toString()
        )
      ) {
        status = this.userStatuses[this.studentId]
      }

      return status
    },
    alertDisabled() {
      return this.buzzed || this.studentStatus !== 'online'
    },
    maxIndex() {
      return this.$store.state.classroom.maxIndex + 100
    },
    isLocked() {
      return this.file?.asset?.isLocked
    },
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    isLockedForStudent() {
      return this.isLocked && this.isStudent
    },
    lessonId() {
      return this.$store.state.classroom.lessonId
    },
    isLessonFinished() {
      return this.$store.getters['classroom/isLessonFinished']
    },
    defaultZoomIndex() {
      return this.minZoom > 1 ? this.minZoom : 1
    },
    acceptedFilesStr() {
      return this.$store.getters['classroom/acceptedFilesStr']
    },
  },
  watch: {
    isLockedForStudent(newValue, oldValue) {
      if (newValue) {
        this.resetCurrentValues()
        this.$store.commit('classroom/closeVideoInput')
      }
    },
  },
  beforeMount() {
    this.arrStatusId = [this.studentId]

    this.refreshStatusOnline()
  },
  beforeDestroy() {
    this.resetCurrentValues()
  },
  methods: {
    selectToolClickHandler(toolName, cursorName) {
      this.currentTool = toolName
      this.currentHorizontalMenu = null

      this.setTool(toolName, cursorName)
    },
    uploadFromComputer(event) {
      this.currentHorizontalMenu = null

      this.uploadFiles(event.target.files)
    },
    buzz() {
      this.buzzed = true

      setTimeout(() => {
        this.buzzed = false
      }, 30000)

      this.$store.dispatch('classroom/buzz', this.lessonId)
    },
    reset() {
      let height, ratio
      let i = 1
      let offsetX = 0
      let offsetY = 0

      this.$store.state.classroom.assets.slice(0).forEach((asset) => {
        const _asset = { ...asset.asset }

        i++

        switch (_asset.type) {
          case 'shape':
          case 'lock':
            break
          case 'editor':
            _asset.width =
              (this.isCanvasOversizeX ? this.viewportWidth : mainCanvasWidth) *
              0.66

            height =
              (this.isCanvasOversizeY
                ? this.viewportHeight
                : mainCanvasHeight) * 0.8

            if (height > 1200) {
              height = 1200
            }

            if (height < 400) {
              height = 400
            }

            _asset.height = height - this.offset * 2
            _asset.top = this.offset
            _asset.left = this.offset
            _asset.index = 1
            break
          case 'twilio':
          case 'tokbox':
          case 'whereby':
            _asset.width = 400
            _asset.height = 300
            _asset.top = this.offset
            _asset.left =
              (this.isCanvasOversizeX ? this.viewportWidth : mainCanvasWidth) -
              _asset.width -
              this.offset
            _asset.index = i
            break
          case 'pdf':
          case 'image':
          case 'video':
          case 'audio':
            ratio = defaultWidth / asset.asset.width

            _asset.width = defaultWidth
            _asset.height = _asset.height * ratio
            _asset.top =
              this.$store.getters['classroom/zoomAsset'].asset.y + offsetY + 100
            _asset.left =
              this.viewportWidth / 2 +
              this.$store.getters['classroom/zoomAsset'].asset.x +
              offsetX -
              250
            _asset.index = i
            offsetX += 50
            offsetY += 50
            break
          case 'zoom':
            _asset.zoomIndex = this.defaultZoomIndex
            _asset.x = 0
            _asset.y = 0
            break
          default:
        }

        this.$store.commit('classroom/moveAsset', {
          id: asset.id,
          asset: _asset,
        })
        this.$store.dispatch('classroom/moveAsset', {
          id: asset.id,
          lessonId: asset.lessonId,
          asset: _asset,
        })
      })
    },
    toggleVideoInput() {
      this.$store.commit('classroom/toggleVideoInput')
    },
    openLibrary() {
      this.currentHorizontalMenu = null

      this.$store.commit('classroom/toggleLibrary')
    },
    toggleStudentRoomStatus() {
      const asset = { isLocked: !this.isLocked }

      this.$store.commit('classroom/moveAsset', {
        id: this.file.id,
        asset,
      })

      this.$store.dispatch('classroom/moveAsset', {
        id: this.file.id,
        lessonId: this.lessonId,
        asset,
      })
    },
    resetCurrentValues() {
      this.currentTool = 'pointer'
      this.currentHorizontalMenu = null
    },
    finishLesson() {
      this.$store
        .dispatch('lesson/finishLesson', this.lessonId)
        .then(() => {
          this.exitLesson()
        })
        .catch((e) => {
          this.$store.dispatch('snackbar/error')

          console.info(e)
        })
    },
    exitLesson() {
      window.location = '/user/lessons' // window.location is needed to trigger event 'user-left-classroom'
    },
  },
}
</script>

<style scoped lang="scss">
.toolbar {
  position: fixed;
  z-index: 99999 !important;
}

label.popup-load-files-label-upload {
  margin-right: 0;
}
.toolbar-buttons-wrapper {
  position: absolute;
  top: 50%;
  right: 2%;
  transform: translateY(-50%);
}

.cursor-pointer,
.cursor-pointer * {
  cursor: pointer !important;
}

.toolbar-buttons {
  margin-bottom: 0;
  padding: 8px 0;
}

.toolbar-buttons-horizontal.toolbar-show,
.toolbar-buttons-horizontal-file.toolbar-show {
  display: flex !important;
}

.toolbar-buttons li {
  list-style: none;
}

.toolbar-button-wrapper form {
  display: inline-block;
  width: 100%;
}

.toolbar-button-wrapper-horizontal {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  position: relative;
}

.toolbar-button-wrapper-pencil > button {
  padding: 9px;
}

.toolbar-button-wrapper-exit button {
  padding-left: 7px;
  padding-right: 10px;
}

.toolbar-button-wrapper-reset button {
  padding-left: 10px;
  padding-right: 10px;
}

.toolbar-button-wrapper-finish button {
  padding-right: 7px;
}

.toolbar-button-wrapper-horizontal-books button {
  padding: 9px;
}

.toolbar-button-wrapper-horizontal-laptop button {
  padding-top: 10px;
}

//.toolbar-button-wrapper-replace,
//.toolbar-button-wrapper-undo {
//  transform: none;
//  padding: 0;
//}
//
//.toolbar-button-wrapper-replace {
//  position: absolute;
//  height: 24px;
//  top: -6px;
//  left: 0;
//  background-color: #fff;
//  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
//  border-radius: 6px;
//  z-index: 2001 !important;
//}
//
//.toolbar-button-replace {
//  width: 100%;
//  height: 100%;
//  max-height: 100%;
//  margin-bottom: 0;
//  padding: 5px 0;
//}
//
//.toolbar-button-undo {
//  padding: 8px 0;
//  margin-bottom: 8px;
//  width: 100%;
//  border-radius: 6px;
//  box-shadow: 0 4px 2px -2px rgba(0, 0, 0, 0.15);
//}

.toolbar-buttons-horizontal > ul {
  display: flex;
  padding: 0 10px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
}

.toolbar-button-wrapper-horizontal-draw-line button {
  padding: 10px 5px 6px 10px;
}

.toolbar-button-wrapper-horizontal-draw-pencil button {
  padding: 9px;
}

.toolbar-button-item-hand {
  padding-left: 0;
}

.toolbar-buttons-horizontal > ul li:first-child,
.toolbar-buttons-horizontal > ul li:first-child button {
  border-bottom-left-radius: 6px !important;
  border-top-left-radius: 6px !important;
}

.toolbar-buttons-horizontal > ul li:last-child,
.toolbar-buttons-horizontal > ul li:last-child button {
  border-bottom-right-radius: 6px !important;
  border-top-right-radius: 6px !important;
}

#toolbar-switch {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.toolbar--student .toolbar-button-wrapper:hover > button:enabled > svg,
.toolbar--student .toolbar-button-wrapper:hover > form > button:enabled > svg,
.toolbar--student .toolbar-button-wrapper-horizontal:hover svg,
.toolbar--student .toolbar-button-item.selected:not(:disabled) svg {
  color: var(--v-studentColor-base) !important;
}

.toolbar--teacher .toolbar-button-wrapper:hover > button:enabled > svg,
.toolbar--teacher .toolbar-button-wrapper:hover > form > button:enabled > svg,
.toolbar--teacher .toolbar-button-wrapper-horizontal:hover svg,
.toolbar--teacher .toolbar-button-item.selected:not(:disabled) svg {
  color: var(--v-teacherColor-base) !important;
}

.toolbar-button-wrapper .toolbar-button-item:disabled svg {
  color: #c6c6c6 !important;
  fill: #c6c6c6 !important;
  stroke: #c6c6c6 !important;
}

.toolbar-button-item svg {
  color: var(--v-darkLight-base);
}

.hover-btn-info-horizontal {
  top: -20px;
  right: -60%;
}

.toolbar-button-replace + .hover-btn-info {
  top: 40%;
}

.selected {
  border-bottom: none;
}
</style>

<!--<style module>-->
<!--.teacher_horizontal_hover {-->
<!--  box-shadow: rgb(127, 184, 2) 0 2px 0 0, rgb(127, 184, 2) -2px -2px 0 0,-->
<!--    rgb(127, 184, 2) -2px 2px 0 0, rgb(127, 184, 2) 2px -2px 0 0;-->
<!--}-->

<!--.teacher_toolbar_hover {-->
<!--  box-shadow: 0 0 2px 2px rgb(127, 184, 2) !important;-->
<!--}-->

<!--.student_horizontal_hover {-->
<!--  box-shadow: rgb(60, 135, 248) 0 2px 0 0, rgb(60, 135, 248) -2px -2px 0 0,-->
<!--    rgb(60, 135, 248) -2px 2px 0 0, rgb(60, 135, 248) 2px -2px 0 0;-->
<!--}-->

<!--.student_toolbar_hover {-->
<!--  box-shadow: 0 0 2px 2px rgb(60, 135, 248) !important;-->
<!--}-->
<!--</style>-->
