import path from 'path'
import fs from 'fs'
import axios from 'axios'
import pkg from './package'
require('dotenv').config()

// eslint-disable-next-line nuxt/no-cjs-in-config
const localeDomains = require('./locale-domains').default

const config = {
  ssr: true,
  target: 'server',

  // PWA module configuration: https://go.nuxtjs.dev/pwa
  pwa: {
    manifest: {
      lang: 'en',
      name: '<PERSON><PERSON>',
      short_name: '<PERSON>u',
      display: 'standalone',
      prefer_related_applications: true,
      background_color: '#f8faff',
      theme_color: '#80b622',
      orientation: 'portrait',
      description: pkg.description,
      start_url: '/?standalone=true',
    },
    icon: {
      source: '/static',
      fileName: 'logo_big.png',
      sizes: [64, 120, 144, 152, 192, 384, 512],
    },
  },

  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    titleTemplate: (titleChunk) => titleChunk || 'Langu',
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { hid: 'description', name: 'description', content: pkg.description },
    ],
    link: [
      { rel: 'icon', type: 'image/x-icon', href: '/images/favicon.ico' },
      {
        rel: 'preconnect',
        href: 'https://fonts.googleapis.com',
      },
      {
        rel: 'preconnect',
        href: 'https://fonts.gstatic.com',
        crossorigin: true,
      },
      {
        rel: 'stylesheet',
        href: '@/assets/styles/_fonts.scss',
      },
    ],
    script: [
      {
        innerHTML: `
          (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-WB9P3RB');
        `,
        type: 'text/javascript',
        charset: 'utf-8',
      },
    ],
    __dangerouslyDisableSanitizers: ['script'],
    // script: [
    //   {
    //     src:
    //       'https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js',
    //     async: true,
    //   },
    // ],
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: ['@/assets/styles/main.scss'],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    { src: '@/plugins/axios' },
    { src: '@/plugins/pluralization' },
    { src: '@/plugins/tidio-chat', mode: 'client' },
    { src: '@/plugins/socket', mode: 'client' },
    { src: '@/plugins/konva', mode: 'client' },
    { src: '@/plugins/vue-rating', mode: 'client' },
    { src: '@/plugins/stripe', mode: 'client' },
    { src: '@/plugins/gtm', mode: 'client' },
    { src: '@/plugins/router.js' },
  ],
  env: {
    STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
  },

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: true,

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // https://go.nuxtjs.dev/pwa
    '@nuxtjs/pwa',
    // https://go.nuxtjs.dev/eslint
    '@nuxtjs/eslint-module',
    // https://go.nuxtjs.dev/vuetify
    '@nuxtjs/vuetify',
  ],

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    // https://go.nuxtjs.dev/axios
    '@nuxtjs/axios',
    [
      'nuxt-i18n',
      {
        detectBrowserLanguage: {
          useCookie: true,
          alwaysRedirect: true,
        },
        strategy: 'prefix_except_default',
        locales: [
          {
            code: 'pl',
            iso: 'pl-PL',
            file: 'pl.js',
            domain: localeDomains.pl.domain,
          },
          {
            code: 'es',
            iso: 'es-ES',
            file: 'es.js',
            domain: localeDomains.es.domain,
          },
          {
            code: 'en',
            file: 'en.js',
            iso: 'en-GB',
            domain: localeDomains.en.domain,
          },
        ],
        differentDomains: true,
        lazy: true,
        langDir: 'lang/',
        vuex: false,
      },
    ],
    ['cookie-universal-nuxt', { alias: 'cookiz' }],
    // '@nuxtjs/gtm',
    '@nuxtjs/dayjs',
    // [
    //   'nuxt-stripe-module',
    //   { publishableKey: process.env.STRIPE_PUBLISHABLE_KEY },
    // ],
    // [
    //   '@nuxtjs/google-gtag',
    //   {
    //     id: process.env.NUXT_ENV_GTAG,
    //   },
    // ],
    // [
    //   '@netsells/nuxt-hotjar',
    //   {
    //     id: process.env.NUXT_ENV_HOTJAR_ID,
    //     sv: process.env.NUXT_ENV_HOTJAR_SV,
    //   },
    // ],
    // [
    //   'nuxt-facebook-pixel-module',
    //   {
    //     pixelId: process.env.NUXT_ENV_FACEBOOK_PIXEL_ID,
    //     track: 'PageView',
    //     autoPageView: true,
    //   },
    // ],
    '@nuxtjs/sentry',
    // ['@nuxtjs/robots', {
    //   rules: [
    //     { UserAgent: '*' },
    //     { Host: process.env.NUXT_ENV_URL },
    //     { Disallow: '/user/' },
    //     { Sitemap: process.env.NUXT_ENV_URL + '/sitemap.xml' },
    //     { Sitemap: process.env.NUXT_ENV_URL + '/es-sitemap-index.xml' },
    //     { Sitemap: process.env.NUXT_ENV_URL + '/pl-sitemap-index.xml' },
    //     { UserAgent: 'Yandex' },
    //     { Disallow: '/en/*' },
    //     { Allow: '/en/$' },
    //     { Disallow: '/es/*' },
    //     { Allow: '/es/$' },
    //     { Disallow: '/pl/*' },
    //     { Allow: '/pl/$' },
    //     { UserAgent: 'Slurp' },
    //     { Host: process.env.NUXT_ENV_URL },
    //     { CrawlDelay: 20 },
    //     { UserAgent: 'msnbot' },
    //     { CrawlDelay: 10 },
    //     { UserAgent: 'bingbot' },
    //     { CrawlDelay: 10 },
    //     { UserAgent: 'SemrushBot' },
    //     { Disallow: '/' },
    //     { UserAgent: 'MozBot' },
    //     { Disallow: '/' },
    //     { UserAgent: 'Sogou' },
    //     { CrawlDelay: 10 }
    //   ]
    // }],
    // '@nuxtjs/sitemap', // must be always the latest
  ],

  // Axios module configuration: https://go.nuxtjs.dev/config-axios
  axios: {
    proxy: false,
  },

  generate: {
    fallback: true,
  },
  gtm: {
    id: 'GTM-WB9P3RB',
  },
  // Vuetify module configuration: https://go.nuxtjs.dev/config-vuetify
  vuetify: {
    treeShake: true,
    customVariables: ['~/assets/styles/vuetify-variables.scss'],
    optionsPath: './vuetify.options.js',
    defaultAssets: false,
  },

  dayjs: {
    locales: Object.keys(localeDomains),
    plugins: [
      'isSameOrAfter',
      'isBetween',
      'isoWeek',
      'utc',
      'timezone',
      'advancedFormat',
      'localizedFormat',
    ],
  },

  sentry: {
    dsn: process.env.NUXT_ENV_SENTRY_DSN,
    disabled: process.env.NODE_ENV === 'development',
    config: {
      autoSessionTracking: false,
      environment: process.env.NUXT_ENV_SENTRY_ENVIRONMENT,
    },
  },
  // Server Middleware
  serverMiddleware: [
    { path: '/_nuxt/api/whereby', handler: '~/api/whereby.js' },
  ],
  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    extend(config, ctx) {
      config.module.rules.push({
        test: /\.(ogg|mp3|wav|mpe?g)$/i,
        loader: 'file-loader',
        options: {
          name: '[path][name].[ext]',
        },
      })
    },
  },

  router: {
    linkActiveClass: 'active',
    middleware: 'redirect',
  },
  // sitemap: [
  //   {
  //     path: '/sitemap.xml',
  //   },
  //   {
  //     path: '/es-sitemap-index.xml',
  //   },
  //   {
  //     path: '/pl-sitemap-index.xml',
  //   },
  // ],
  // sitemap: [
  //   {
  //     hostname: process.env.NUXT_ENV_URL,
  //     exclude: ['/user/settings', '/user/lessons', '/user/messages'],
  //     path: '/sitemap.xml',
  // routes: async () => {
  //   const config = {
  //     withCredentials: true,
  //   }

  //   const username = process.env.NUXT_ENV_API_USERNAME
  //   const password = process.env.NUXT_ENV_API_PASSWORD

  //   if (username && password) {
  //     config.auth = {
  //       username,
  //       password,
  //     }
  //   }

  //   const { data } = await axios.get(
  //     `${process.env.NUXT_ENV_API_URL}/users/available-teachers-username`,
  //     config
  //   )

  //   return JSON.parse(data).map((username) => ({
  //     url: `/teacher/${username}`,
  //     changefreq: 'monthly',
  //     priority: 1,
  //   }))
  // },
  // },
  // {
  //   hostname: process.env.NUXT_ENV_URL,
  //   exclude: ['/user/settings', '/user/lessons', '/user/messages'],
  //   path: '/es-sitemap-index.xml',
  // routes: async () => {
  //   const config = {
  //     withCredentials: true,
  //   }

  //   const username = process.env.NUXT_ENV_API_USERNAME
  //   const password = process.env.NUXT_ENV_API_PASSWORD

  //   if (username && password) {
  //     config.auth = {
  //       username,
  //       password,
  //     }
  //   }

  //   const { data } = await axios.get(
  //     `${process.env.NUXT_ENV_API_URL}/users/available-teachers-username`,
  //     config
  //   )

  //   return JSON.parse(data).map((username) => ({
  //     url: `/teacher/${username}`,
  //     changefreq: 'monthly',
  //     priority: 1,
  //   }))
  // },
  // },
  // {
  //   hostname: process.env.NUXT_ENV_URL,
  //   exclude: ['/user/settings', '/user/lessons', '/user/messages'],
  //   path: '/pl-sitemap-index.xml',
  // routes: async () => {
  //   const config = {
  //     withCredentials: true,
  //   }

  //   const username = process.env.NUXT_ENV_API_USERNAME
  //   const password = process.env.NUXT_ENV_API_PASSWORD

  //   if (username && password) {
  //     config.auth = {
  //       username,
  //       password,
  //     }
  //   }

  //   const { data } = await axios.get(
  //     `${process.env.NUXT_ENV_API_URL}/users/available-teachers-username`,
  //     config
  //   )

  //   return JSON.parse(data).map((username) => ({
  //     url: `/teacher/${username}`,
  //     changefreq: 'monthly',
  //     priority: 1,
  //   }))
  // },
  //   },
  // ],
}

if (process.env.NODE_ENV === 'development') {
  config.server = {
    https: {
      key: fs.readFileSync(path.resolve(__dirname, 'ssl.key')),
      cert: fs.readFileSync(path.resolve(__dirname, 'ssl.crt')),
    },
  }

  config.modules.push('@nuxtjs/proxy')

  config.axios.proxy = true
  config.proxy = {
    '/api/proxy': {
      target: process.env.NUXT_ENV_LOCAL_API_URL,
      pathRewrite: { '/api/proxy': '' },
      changeOrigin: true,
      headers: {
        Connection: 'keep-alive',
      },
      secure: false,
      onProxyReq: function log(proxyReq, req) {
        if (!req.body || !Object.keys(req.body).length) {
          return
        }

        const contentType = proxyReq.getHeader('Content-Type')
        const writeBody = (bodyData) => {
          proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData))
          proxyReq.write(bodyData)
        }
        if (
          contentType.includes('application/json') ||
          contentType.includes('application/x-www-form-urlencoded')
        ) {
          writeBody(JSON.stringify(req.body))
        }
      },
    },
    '/uploads/resources': {
      target: process.env.NUXT_ENV_PORT_4061_URL,
      changeOrigin: true,
      secure: false,
    },
    '/uploads/canvas': {
      target: process.env.NUXT_ENV_PORT_4061_URL,
      changeOrigin: true,
      secure: false,
    },
    '/websocket': {
      target: process.env.NUXT_ENV_SOCKET_URL,
      changeOrigin: true,
      secure: false,
    },
    '/api/convert': {
      target: process.env.NUXT_ENV_PORT_4061_URL,
      changeOrigin: true,
      secure: false,
    },
  }
}

// Dyanmically generate robots.txt
config.hooks = {
  build: {
    before(builder) {
      const productionRobots = `User-agent: *
Host: https://heylangu.com
 
# General Disallow Rules for All Crawlers
Disallow: /user/
 
# Sitemap Locations
Sitemap: https://heylangu.com/sitemap.xml
Sitemap: https://heylangu.com/es-sitemap-index.xml
Sitemap: https://heylangu.com/pl-sitemap-index.xml
 
User-agent: Yandex
Disallow: /en/*
Allow: /en/$
Disallow: /es/*
Allow: /es/$
Disallow: /pl/*
Allow: /pl/$
 
Crawl-delay: 2
 
User-agent: Slurp
Host: https://heylangu.com
Crawl-delay: 20
 
User-agent: msnbot
Crawl-delay: 10
 
User-agent: bingbot
Crawl-delay: 10
 
User-agent: SemrushBot
Disallow: /
 
User-agent: MozBot
Disallow: /
 
User-agent: Sogou
Crawl-delay: 10`

      const disallowAllRobots = `User-agent: *
Disallow: /`

      const robotsContent =
        process.env.NUXT_ENV_NODE_ENV === 'production'
          ? productionRobots
          : disallowAllRobots

      // Ensure static directory exists
      const staticDir = path.resolve(__dirname, 'static')
      if (!fs.existsSync(staticDir)) {
        fs.mkdirSync(staticDir, { recursive: true })
      }

      // Write robots.txt
      fs.writeFileSync(path.resolve(staticDir, 'robots.txt'), robotsContent)
      console.log(
        `Generated robots.txt for ${process.env.NUXT_ENV_NODE_ENV} environment`
      )
    },
  },
}

export default config
