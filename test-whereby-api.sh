#!/bin/bash

# Test script for Whereby API deployment
# Usage: ./test-whereby-api.sh [domain]
# Example: ./test-whereby-api.sh langu.io

DOMAIN=${1:-langu.io}
BASE_URL="https://$DOMAIN"

echo "Testing Whereby API on $BASE_URL"
echo "=================================="

# Test 1: Health check
echo "1. Testing health endpoint..."
HEALTH_RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" "$BASE_URL/api/whereby/health")
HTTP_CODE=$(echo "$HEALTH_RESPONSE" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$HEALTH_RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')

echo "   Status Code: $HTTP_CODE"
if [ "$HTTP_CODE" = "200" ]; then
    echo "   ✅ Health check passed"
    echo "   Response: $RESPONSE_BODY"
else
    echo "   ❌ Health check failed"
    echo "   Response: $RESPONSE_BODY"
fi
echo ""

# Test 2: Create room endpoint
echo "2. Testing create room endpoint..."
CREATE_RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -d '{"lessonId": "test-deployment-123"}' \
    "$BASE_URL/api/whereby/create-room")

HTTP_CODE=$(echo "$CREATE_RESPONSE" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$CREATE_RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')

echo "   Status Code: $HTTP_CODE"
if [ "$HTTP_CODE" = "200" ]; then
    echo "   ✅ Create room test passed"
    echo "   Response: $RESPONSE_BODY"
elif [ "$HTTP_CODE" = "500" ]; then
    echo "   ⚠️  Server error (middleware loaded but API call failed)"
    echo "   Response: $RESPONSE_BODY"
else
    echo "   ❌ Create room test failed"
    echo "   Response: $RESPONSE_BODY"
fi
echo ""

# Test 3: Check if response is HTML (indicates middleware not loaded)
if echo "$RESPONSE_BODY" | grep -q "<!DOCTYPE"; then
    echo "❌ CRITICAL: API is returning HTML instead of JSON"
    echo "   This indicates the server middleware is not deployed or not working"
    echo "   The /api/whereby endpoint is likely returning a 404 page"
else
    echo "✅ API is returning JSON (middleware is loaded)"
fi

echo ""
echo "Deployment test completed."
