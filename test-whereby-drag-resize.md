# Testing Whereby Drag and Resize Functionality

## Test Steps

### 1. Visual Verification
1. **Open a classroom session**
2. **Look for the Whereby video window**
3. **Check for the header area:**
   - Should see a dark semi-transparent header at the top
   - Header should display "Whereby Video Call"
   - Header should be about 30px tall

### 2. Hover Test
1. **Hover over the header area**
   - ✅ Should see the green hand cursor (grab cursor)
   - ✅ Header should darken slightly on hover
   - ✅ Cursor should change to the role-specific grab cursor (teacher/student)

2. **Hover over the video area (below header)**
   - ✅ Should still allow normal video interaction
   - ✅ Whereby controls should work normally

### 3. Drag Test
1. **Click and hold on the header area**
   - ✅ Should be able to drag the entire video window
   - ✅ Video should move smoothly around the classroom
   - ✅ Position should be saved and synchronized

2. **Try dragging from different parts of the header**
   - ✅ Should work from any part of the header area
   - ✅ Should not interfere with video controls

### 4. Resize Test
1. **Hover over the edges/corners of the video window**
   - ✅ Should see resize handles appear
   - ✅ Cursor should change to resize cursor

2. **Drag the resize handles**
   - ✅ Should resize the video window
   - ✅ Should maintain aspect ratio
   - ✅ Header should resize with the window

### 5. Z-Index Test
1. **Click on the video window**
   - ✅ Should bring the window to front
   - ✅ Should work with multiple elements in classroom

### 6. Functionality Test
1. **Verify video still works**
   - ✅ Video call should function normally
   - ✅ Audio/video controls should work
   - ✅ Screen sharing should work
   - ✅ All Whereby features should be accessible

## Expected Behavior

### Header Area
- **Appearance:** Dark semi-transparent bar at top of video
- **Text:** "Whereby Video Call" in white text
- **Hover:** Darkens on hover, shows grab cursor
- **Function:** Primary drag handle for moving the window

### Drag Functionality
- **Cursor:** Green hand cursor (role-specific)
- **Movement:** Smooth dragging anywhere in classroom
- **Persistence:** Position saved and synchronized

### Resize Functionality
- **Handles:** Appear on hover at edges/corners
- **Behavior:** Maintains aspect ratio
- **Limits:** Respects minimum/maximum sizes

### Video Functionality
- **Iframe:** Should not be blocked by drag functionality
- **Controls:** All Whereby controls should work
- **Performance:** No impact on video quality or performance

## Troubleshooting

### If hover cursor doesn't appear:
1. Check that `:hover-enabled="true"` is set
2. Verify CSS classes are applied correctly
3. Check browser developer tools for CSS conflicts

### If dragging doesn't work:
1. Verify ClassroomContainer is properly configured
2. Check that updateData method is being called
3. Ensure store integration is working

### If video is blocked:
1. Check iframe pointer-events styling
2. Verify header z-index is correct
3. Test with different browsers

## Browser Compatibility

- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari
- ✅ Edge

## Performance Notes

- Header overlay has minimal performance impact
- Drag/resize uses existing ClassroomContainer infrastructure
- No additional JavaScript libraries required
- Maintains all existing Whereby functionality
